import React, { useMemo } from "react";
import { useNodeQuery } from "@/app/services/common.services";
import Loader from "@/app/components/Loader";
import { View } from "react-native";
import AppText from "@/app/components/AppText";
import colors from "@/app/config/colors";
import HR from "@/app/components/HR";
import layout from "@/app/config/layout";
import useAppRoute from "@/app/hooks/useAppRoute";
import AppLayout from "@/app/components/AppLayout";
import spacing from "@/app/config/spacing";
import { AppImage } from "@/app/components/AppImage";
import ImageModal from "react-native-image-modal";
import device from "@/app/config/device";
import { FamilyMembers } from "../../Home/components/FamilyMembers";
import {
  Female02Icon,
  Male02Icon,
  MoreVerticalIcon,
} from "@hugeicons-pro/core-stroke-standard";
import { Badge } from "@/app/components/Badge";
import { Bounceable } from "@/app/components/Bounceable";
import { SheetManager } from "react-native-actions-sheet";
import { HugeiconsIcon } from "@hugeicons/react-native";

export const NodeScreen = () => {
  const {
    params: { nodeId },
  } = useAppRoute<"NodeScreen">();

  const { data: node, isPending } = useNodeQuery(nodeId, {
    include: [
      "children",
      "country",
      "city",
      "district",
      "parent",
      "wives",
      "husbands",
      "siblings",
    ],
  });

  const depth = useMemo(() => node?.full_name?.split(/بن|بنت/g).length, [node]);

  if (isPending) {
    return <Loader insideAppLayout />;
  }

  if (!node) {
    return null;
  }

  return (
    <AppLayout
      title={
        <View>
          <AppText size="md" textAlign="center" weight="medium">
            {node.name}
          </AppText>
          <View style={[layout.row, layout.center, spacing.mt1, layout.gap(8)]}>
            {depth ? (
              <AppText
                size="xs"
                textAlign="center"
                color={colors.gray["600"]}
              >{`المستوى ${depth}`}</AppText>
            ) : null}

            <AppText>•</AppText>
            <AppText size="xs" textAlign="center" color={colors.gray["600"]}>
              {`الرقم التسلسي: ${node.id}`}
            </AppText>
          </View>
        </View>
      }
      headerEnd={
        <Bounceable
          onPress={() =>
            SheetManager.show("node-options-sheet", { payload: { node } })
          }
        >
          <HugeiconsIcon
            icon={MoreVerticalIcon}
            color={colors.gray["800"]}
            width={32}
            height={32}
          />
        </Bounceable>
      }
      disablePadding
    >
      {node.photo_url ? (
        <ImageModal
          style={{ width: device.width, height: 250 }}
          source={{ uri: node.photo_url }}
          renderImageComponent={({ source, style }) => (
            <AppImage style={style} source={source} />
          )}
        />
      ) : null}
      <View style={[spacing.px3, spacing.py2]}>
        <View style={[layout.justifyContent.spaceBetween, layout.row]}>
          <View style={[layout.gap(6)]}>
            <View style={[layout.row, layout.gap(12)]}>
              <AppText color={colors.gray["600"]}>الاسم</AppText>
              <AppText containerStyle={{ width: "69%" }} weight="medium">
                {node.full_name}
              </AppText>
              <View style={[layout.gap(8), layout.alignItems.center]}>
                {node.gender === "male" ? (
                  <HugeiconsIcon
                    icon={Male02Icon}
                    width={24}
                    height={24}
                    color={colors.blue["700"]}
                  />
                ) : (
                  <HugeiconsIcon
                    icon={Female02Icon}
                    width={24}
                    height={24}
                    color={colors.pink["700"]}
                  />
                )}
                {node.life_status === "alive" ? (
                  <Badge size="xs" color="green">
                    حي
                  </Badge>
                ) : (
                  <Badge size="xs" color="red">
                    متوفي
                  </Badge>
                )}
              </View>
            </View>
            <View style={[layout.row, layout.gap(12)]}>
              <AppText color={colors.gray["600"]}>الجوال</AppText>
              <AppText weight="medium">{node.mobile}</AppText>
            </View>
            <View style={[layout.row, layout.gap(12)]}>
              <AppText color={colors.gray["600"]}>البريد الإلكتروني</AppText>
              <AppText weight="medium">{node.email}</AppText>
            </View>
          </View>
        </View>
        <HR size={30} />

        <View style={[layout.gap(6)]}>
          <View style={[layout.row, layout.gap(12)]}>
            <AppText color={colors.gray["600"]}>الدولة</AppText>
            <AppText weight="medium">{node.country?.name}</AppText>
          </View>
          <View style={[layout.row, layout.gap(12)]}>
            <AppText color={colors.gray["600"]}>المدينة</AppText>
            <AppText weight="medium">{node.city?.name}</AppText>
          </View>
          <View style={[layout.row, layout.gap(12)]}>
            <AppText color={colors.gray["600"]}>الحي</AppText>
            <AppText weight="medium">{node.district?.name}</AppText>
          </View>
        </View>
        <HR size={30} />

        {node.about ? (
          <>
            <AppText color={colors.gray["600"]}>نبذة</AppText>

            <AppText weight="medium">{node.about}</AppText>

            <HR size={30} />
          </>
        ) : null}
        <FamilyMembers node={node} />
      </View>
    </AppLayout>
  );
};
