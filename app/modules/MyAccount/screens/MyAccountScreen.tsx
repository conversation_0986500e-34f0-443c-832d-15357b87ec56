import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import React from "react";
import { <PERSON><PERSON>, ScrollView, TouchableOpacity, View } from "react-native";
import DeviceInfo from "react-native-device-info";
import * as Notifications from "expo-notifications";

import AppLayout from "../../../components/AppLayout";
import AppText from "../../../components/AppText";
import colors from "../../../config/colors";
import layout from "../../../config/layout";
import useUser from "../../../hooks/useUser";
import { deleteUser } from "../services/MyAccount.services";
import { MyFamily } from "@/app/modules/Home/components/MyFamily";
import { AboutFamily } from "@/app/modules/Home/components/AboutFamily";
import { SocialMediaAccounts } from "@/app/modules/Home/components/SocialMediaAccounts";
import { YouLoggedInAsNotMemberBanner } from "@/app/components/YouLoggedInAsNotMemberBanner";
import spacing from "@/app/config/spacing";
import MyAccountItem from "../components/MyAccountItem";
import {
  AddTeam02Icon,
  MailEdit02Icon,
} from "@hugeicons-pro/core-stroke-standard";
import useAppNavigation from "@/app/hooks/useAppNavigation";
import { Box } from "@/app/components/Box";
import AppButton from "@/app/components/AppButton";
import { HugeiconsIcon } from "@hugeicons/react-native";

const MyAccountScreen = () => {
  const { user, clear, logout } = useUser();
  const navigation = useAppNavigation();
  const queryClient = useQueryClient();

  const { data: notificationStatus } = useQuery({
    queryKey: ["notificationPermission"],
    queryFn: async () => (await Notifications.getPermissionsAsync()).status,
  });

  const { mutate: requestNotificationPermission } = useMutation({
    mutationFn: async () =>
      (await Notifications.requestPermissionsAsync()).status,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["notificationPermission"] });
    },
  });

  const { mutate: deleteUserMutate } = useMutation({
    mutationKey: ["deleteUser"],
    onSuccess: () => clear(),
    mutationFn: deleteUser,
  });

  const deleteAccount = () => {
    Alert.alert("هل أنت متأكد؟", "", [
      {
        text: "نعم، احذف حسابي",
        onPress: () => deleteUserMutate(),
        style: "destructive",
      },
      {
        text: "لا",
      },
    ]);
  };

  if (!user) {
    return null;
  }

  return (
    <AppLayout showHeader={false}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <MyFamily />
        {user.resource_name === "member" && (
          <Box padding containerStyle={[spacing.mt4, spacing.mb4]}>
            <AppText weight="bold" size="lg" containerStyle={[spacing.mb3]}>
              طلباتي
            </AppText>
            <View style={[layout.gap(16)]}>
              <MyAccountItem
                label="طلبات إضافة الأفراد"
                icon={
                  <HugeiconsIcon
                    icon={AddTeam02Icon}
                    color={colors.gray["900"]}
                    width={24}
                    height={24}
                  />
                }
                onPress={() => navigation.navigate("MyNodeAdditionsScreen")}
              />
              <MyAccountItem
                label="طلبات تعديل الأفراد"
                icon={
                  <HugeiconsIcon
                    icon={MailEdit02Icon}
                    color={colors.gray["900"]}
                    width={24}
                    height={24}
                  />
                }
                onPress={() => navigation.navigate("MyNodeChangesScreen")}
              />
            </View>
          </Box>
        )}

        <AboutFamily />
        <SocialMediaAccounts />
        <View>
          <AppText weight="bold" size="xl">
            {user.name}
          </AppText>
          <AppText size="md">{user.mobile}</AppText>
        </View>

        <View style={[layout.gap(12), spacing.mt4, spacing.mb4]}>
          <TouchableOpacity
            onPress={() => logout()}
            style={[layout.row, layout.justifyContent.spaceBetween]}
          >
            <View style={[layout.row, layout.center]}>
              <AppText color={colors.red["500"]}>تسجيل الخروج</AppText>
            </View>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={deleteAccount}
            style={[layout.row, layout.justifyContent.spaceBetween]}
          >
            <View style={[layout.row, layout.center]}>
              <AppText color={colors.red["500"]}>حذف الحساب</AppText>
            </View>
          </TouchableOpacity>
          {notificationStatus !== "granted" && (
            <AppButton
              size="sm"
              type="outline"
              label="تفعيل الإشعارات"
              onPress={() => requestNotificationPermission()}
            />
          )}

          <AppText textAlign="center">
            {DeviceInfo.getReadableVersion()}
          </AppText>
          <YouLoggedInAsNotMemberBanner />
        </View>
      </ScrollView>
    </AppLayout>
  );
};

export default MyAccountScreen;
