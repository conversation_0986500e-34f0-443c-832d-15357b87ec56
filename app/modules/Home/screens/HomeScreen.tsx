import React, { useMemo } from "react";
import { View } from "react-native";
import AppLayout from "../../../components/AppLayout";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import spacing from "@/app/config/spacing";
import useUser from "@/app/hooks/useUser";
import { Greetings } from "@/app/modules/Home/components/Greetings";
import { useSettingsQuery } from "@/app/services/common.services";
import { AppImage } from "@/app/components/AppImage";
import { getSize } from "@/app/helpers";
import rounded from "@/app/config/rounded";
import layout from "@/app/config/layout";
import { NodeSearchInput } from "@/app/components/NodeSearchInput";
import useAppNavigation, {
  useNavigateToNode,
} from "@/app/hooks/useAppNavigation";
import { YouLoggedInAsNotMemberBanner } from "@/app/components/YouLoggedInAsNotMemberBanner";
import { useNotification } from "@/app/hooks/useNotification";
import {
  Tree01Icon,
  MailEdit02Icon,
  Chart01Icon,
  ViewIcon,
  AddTeam02Icon,
  ScrollIcon,
} from "@hugeicons-pro/core-stroke-standard";
import AppText from "@/app/components/AppText";
import colors from "@/app/config/colors";
import { useQuickStats } from "@/app/modules/Home/services/home.services";
import { Bounceable } from "@/app/components/Bounceable";
import { HugeiconsIcon } from "@hugeicons/react-native";

const HomeScreen = () => {
  useNotification();

  const navigateToNode = useNavigateToNode();
  const navigation = useAppNavigation();
  const insets = useSafeAreaInsets();
  const { user } = useUser();
  const { data: settings } = useSettingsQuery();
  const { data: stats } = useQuickStats();

  const logo = useMemo(
    () => settings?.find((s) => s.key === "family_logo")?.value,
    [settings],
  );

  const cards = [
    {
      title: "الشجرة",
      icon: <HugeiconsIcon icon={Tree01Icon} color={colors.white} size={28} />,
      onPress: () => navigation.navigate("Tree"),
    },
    {
      title: "طلبات الإضافة",
      icon: (
        <HugeiconsIcon icon={MailEdit02Icon} color={colors.white} size={28} />
      ),
      onPress: () =>
        navigation.navigate("MyAccount", { screen: "MyNodeAdditionsScreen" }),
    },
    {
      title: "طلبات التعديل",
      icon: (
        <HugeiconsIcon icon={AddTeam02Icon} color={colors.white} size={28} />
      ),
      onPress: () =>
        navigation.navigate("MyAccount", { screen: "MyNodeChangesScreen" }),
    },
    {
      title: "أخبار العائلة",
      icon: <HugeiconsIcon icon={ViewIcon} color={colors.white} size={28} />,
      onPress: () => navigation.navigate("PostsScreen"),
    },
    {
      title: "المستندات",
      icon: <HugeiconsIcon icon={ScrollIcon} color={colors.white} size={28} />,
      onPress: () => navigation.navigate("DocumentsScreen"),
    },
    {
      title: "الإحصائيات",
      icon: <HugeiconsIcon icon={Chart01Icon} color={colors.white} size={28} />,
      subtitle: stats
        ? `${stats.femaleNumber + stats.maleNumber} فردًا`
        : "...",
      onPress: () => navigation.navigate("Stats"),
    },
  ];

  if (!user) {
    return null;
  }

  return (
    <AppLayout showHeader={false} disablePadding disableSafeArea>
      <View style={[{ paddingTop: insets.top + 20 }, spacing.px3]}>
        <YouLoggedInAsNotMemberBanner />
        <View style={[spacing.mb6, spacing.mt2]}>
          <NodeSearchInput onSelectNode={(node) => navigateToNode(node.id)} />
        </View>
        {logo ? (
          <View style={[layout.center, spacing.my6]}>
            <AppImage
              style={[getSize(100, 100), rounded.lg]}
              source={{ uri: logo }}
            />
          </View>
        ) : null}
        <Greetings />

        {/* Quick Action Cards */}
        <View style={[spacing.mt6, spacing.mb8]}>
          <View style={[layout.row, layout.wrap, layout.gap(16)]}>
            {cards.map((card, index) => (
              <Bounceable
                key={index}
                contentContainerStyle={{
                  height: 150,
                  width: "47.5%",
                  borderRadius: 16,
                  backgroundColor: colors.green["500"],
                  padding: 12,
                  justifyContent: "center",
                  alignItems: "center",
                }}
                onPress={card.onPress}
              >
                <View
                  style={[
                    {
                      backgroundColor: "rgba(255, 255, 255, 0.2)",
                    },
                    spacing.p4,
                    spacing.mb2,
                    rounded.full,
                    getSize(10, 10),
                    layout.center,
                  ]}
                >
                  {card.icon}
                </View>
                <View style={[layout.center, layout.gap(4)]}>
                  <AppText
                    size="lg"
                    weight="bold"
                    textAlign="center"
                    color={colors.white}
                  >
                    {card.title}
                  </AppText>
                  {card.subtitle && (
                    <AppText
                      size="sm"
                      textAlign="center"
                      color="rgba(255, 255, 255, 0.8)"
                    >
                      {card.subtitle}
                    </AppText>
                  )}
                </View>
              </Bounceable>
            ))}
          </View>
        </View>
      </View>
    </AppLayout>
  );
};

export default HomeScreen;
