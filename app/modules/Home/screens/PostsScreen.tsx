import React from "react";
import { FlatList, View, Pressable } from "react-native";
import AppLayout from "../../../components/AppLayout";
import { usePosts } from "@/app/modules/Home/services/home.services";
import spacing from "@/app/config/spacing";
import Loader from "@/app/components/Loader";
import { AppImage } from "@/app/components/AppImage";
import { getSize, truncate } from "@/app/helpers";
import rounded from "@/app/config/rounded";
import layout from "@/app/config/layout";
import Subtitle from "@/app/components/Subtitle";
import AppText from "@/app/components/AppText";
import colors from "@/app/config/colors";
import dayjs, { toHijri } from "@/app/config/dayjs";
import MaterialCommunityIcons from "@expo/vector-icons/MaterialCommunityIcons";
import useAppNavigation from "@/app/hooks/useAppNavigation";
import { captureEvent, EventType } from "@/app/services/posthog.services";
import { Box } from "@/app/components/Box";

const PostsScreen = () => {
  const { posts, isLoading, fetchNextPage, canFetchMore } = usePosts();
  const navigation = useAppNavigation();

  const renderPost = ({ item: post }) => (
    <View style={[layout.row, layout.gap(12), spacing.p4]}>
      <AppImage
        style={[getSize(50, 50), rounded.full]}
        source={{ uri: post.image_url }}
      />

      <View style={layout.flex1}>
        <View style={[layout.row, layout.gap(8), layout.alignItems.center]}>
          <Subtitle>{post.title}</Subtitle>
        </View>

        <View style={[spacing.mt2]}>
          <View style={[layout.row, layout.alignItems.center, layout.gap(4)]}>
            <AppText color={colors.gray["600"]} size="xs">
              {truncate(post.content ?? "", 50)}
            </AppText>
          </View>
        </View>

        {(post.start_at || post.end_at) && (
          <View style={[layout.row, layout.gap(16), spacing.mt2]}>
            {post.start_at && (
              <View>
                <View
                  style={[
                    layout.row,
                    layout.alignItems.center,
                    layout.gap(4),
                  ]}
                >
                  <MaterialCommunityIcons
                    name="clock-start"
                    size={14}
                    color={colors.gray["600"]}
                  />
                  <AppText color={colors.gray["600"]} size="xs">
                    {dayjs(post.start_at).format("DD MMMM YYYY")}
                  </AppText>
                </View>
                <AppText color={colors.gray["500"]} size="xs">
                  {toHijri(dayjs(post.start_at)).full}
                </AppText>
              </View>
            )}

            {post.end_at && (
              <View>
                <View
                  style={[
                    layout.row,
                    layout.alignItems.center,
                    layout.gap(4),
                  ]}
                >
                  <MaterialCommunityIcons
                    name="clock-end"
                    size={14}
                    color={colors.gray["600"]}
                  />
                  <AppText color={colors.gray["600"]} size="xs">
                    {dayjs(post.end_at).format("DD MMMM YYYY")}
                  </AppText>
                </View>
                <AppText color={colors.gray["500"]} size="xs">
                  {toHijri(dayjs(post.end_at)).full}
                </AppText>
              </View>
            )}
          </View>
        )}
      </View>

      <Pressable
        onPress={() => {
          captureEvent(EventType.POST_VIEW);
          navigation.navigate("PostScreen", { post });
        }}
        style={[
          layout.alignItems.center,
          layout.justifyContent.center,
          spacing.px2,
        ]}
      >
        <MaterialCommunityIcons
          name="eye"
          size={24}
          color={colors.gray["500"]}
        />
      </Pressable>
    </View>
  );

  if (isLoading) {
    return <Loader insideAppLayout />;
  }

  return (
    <AppLayout title="أخبار العائلة">
      <View style={[spacing.px3]}>
        {posts && posts.length > 0 ? (
          <FlatList
            data={posts}
            renderItem={renderPost}
            keyExtractor={(item) => item.id.toString()}
            onEndReached={() => canFetchMore && fetchNextPage()}
            onEndReachedThreshold={0.5}
            ItemSeparatorComponent={() => <View style={[spacing.my2]} />}
            showsVerticalScrollIndicator={false}
          />
        ) : (
          <Box padding>
            <AppText textAlign="center">لا توجد أخبار</AppText>
          </Box>
        )}
      </View>
    </AppLayout>
  );
};

export default PostsScreen;
