import spacing from "@/app/config/spacing";
import { View } from "react-native";
import layout from "@/app/config/layout";
import colors from "@/app/config/colors";
import Title from "@/app/components/Title";
import Subtitle from "@/app/components/Subtitle";
import { Badge } from "@/app/components/Badge";
import React from "react";
import useUser from "@/app/hooks/useUser";
import { InLoveIcon } from "@hugeicons-pro/core-stroke-standard";
import { NodeBadge } from "@/app/components/NodeBadge";
import { Relationship } from "@/app/services/common.services";
import { Box } from "@/app/components/Box";
import { HugeiconsIcon } from "@hugeicons/react-native";

export const MyFamily = () => {
  const { user } = useUser();

  if (!user?.node) {
    return null;
  }

  return (
    <Box padding>
      <View style={[layout.row, layout.alignItems.center, layout.gap(6)]}>
        <HugeiconsIcon icon={InLoveIcon} color={colors.gray["800"]} />
        <Title color={colors.gray["700"]}>عائلتي</Title>
      </View>

      <View style={[spacing.p2]}>
        {user.node.parent ? (
          <>
            <Subtitle>الأب</Subtitle>
            <View style={[layout.row, spacing.mt1, layout.wrap, layout.gap(6)]}>
              <NodeBadge node={user.node.parent} />
            </View>
          </>
        ) : null}
        {user.relationships.length > 0 ? (
          <>
            <Subtitle containerStyle={[spacing.mt4]}>
              {user.relationships.length > 1 ? "الأزواج" : "الزوج"}
            </Subtitle>
            <View style={[layout.row, spacing.mt1, layout.wrap, layout.gap(6)]}>
              {user.relationships.map((relationship: Relationship) =>
                relationship.wife || relationship.husband ? (
                  <NodeBadge
                    key={relationship.id}
                    node={relationship.wife ?? relationship.husband}
                  />
                ) : (
                  <Badge
                    key={relationship.id}
                    color={user.node?.gender === "male" ? "pink" : "blue"}
                  >
                    {relationship.name}
                  </Badge>
                ),
              )}
            </View>
          </>
        ) : null}
        {user.node.siblings && user.node.siblings?.length > 0 ? (
          <>
            <Subtitle containerStyle={[spacing.mt4]}>الأخوة والأخوات</Subtitle>
            <View style={[layout.row, spacing.mt1, layout.wrap, layout.gap(6)]}>
              {user.node.siblings.map((sibling) => (
                <NodeBadge key={sibling.id} node={sibling} />
              ))}
            </View>
          </>
        ) : null}

        {user.node.children && user.node.children.length > 0 ? (
          <>
            <Subtitle containerStyle={[spacing.mt4]}>الأبناء والبنات</Subtitle>
            <View style={[layout.row, spacing.mt1, layout.wrap, layout.gap(6)]}>
              {user.node.children.map((child) => (
                <NodeBadge key={child.id} node={child} />
              ))}
            </View>
          </>
        ) : null}
      </View>
    </Box>
  );
};
