import spacing from "@/app/config/spacing";
import { View } from "react-native";
import layout from "@/app/config/layout"; // import { InLoveIcon } from "@hugeicons/react-native-pro";
import colors from "@/app/config/colors";
import Title from "@/app/components/Title";
import React from "react";
import { useSettingsQuery } from "@/app/services/common.services";
import { LicenseIcon } from "@hugeicons-pro/core-stroke-standard";
import AppText from "@/app/components/AppText";
import { Box } from "@/app/components/Box";
import { HugeiconsIcon } from "@hugeicons/react-native";

export const AboutFamily = () => {
  const { data } = useSettingsQuery();

  if (!data) {
    return null;
  }

  const about = data.find((setting) => setting.key === "about")?.value;

  if (!about) {
    return null;
  }

  return (
    <Box padding>
      <View style={[layout.row, layout.alignItems.center, layout.gap(6)]}>
        <HugeiconsIcon icon={LicenseIcon} />
        <Title color={colors.gray["700"]}>نبذة</Title>
      </View>

      <AppText containerStyle={[spacing.mt3]}>{about}</AppText>
    </Box>
  );
};
