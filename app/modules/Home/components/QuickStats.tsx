import spacing from "@/app/config/spacing";
import { View } from "react-native";
import layout from "@/app/config/layout";
import colors from "@/app/config/colors";
import Title from "@/app/components/Title";
import React from "react";
import { useQuickStats } from "@/app/modules/Home/services/home.services";
import useAppNavigation from "@/app/hooks/useAppNavigation";
import AppText from "@/app/components/AppText";
import Subtitle from "@/app/components/Subtitle";
import HR from "@/app/components/HR";
import { NumberOfNodesStats } from "@/app/modules/Home/components/NumberOfNodesStats";
import { StarsIcon } from "@hugeicons-pro/core-stroke-standard";
import { HugeiconsIcon } from "@hugeicons/react-native";

export const QuickStats = () => {
  const { data: stats } = useQuickStats();
  const navigation = useAppNavigation();

  if (!stats) {
    return null;
  }

  return (
    <>
      <View style={[spacing.mt8]}>
        <View style={[layout.row, layout.alignItems.center, layout.gap(6)]}>
          <HugeiconsIcon icon={StarsIcon} />
          <Title color={colors.gray["700"]}>إحصائيات</Title>
        </View>
        <NumberOfNodesStats stats={stats} />
        <HR containerStyle={[{ width: "75%" }, spacing.my4]} />
        <Subtitle
          color={colors.gray["600"]}
          textAlign="center"
          containerStyle={[spacing.mb2]}
        >
          أكثر الأسماء
        </Subtitle>
        <View
          style={[
            layout.row,
            layout.justifyContent.spaceAround,
            layout.alignItems.center,
          ]}
        >
          {stats.mostCommonNames.map((node, i) => {
            return (
              <View key={i}>
                <AppText size="xl" textAlign="center" weight="bold">
                  {node.count}
                </AppText>

                <AppText size="lg" textAlign="center" weight="medium">
                  {node.name}
                </AppText>
              </View>
            );
          })}
        </View>
      </View>
    </>
  );
};
