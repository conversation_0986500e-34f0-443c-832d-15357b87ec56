import spacing from "@/app/config/spacing";
import { View } from "react-native";
import layout from "@/app/config/layout";
import React, { useMemo } from "react";
import { useSettingsQuery } from "@/app/services/common.services";
import {
  WhatsappIcon,
  SnapchatIcon,
  InstagramIcon,
  YoutubeIcon,
  TwitterIcon,
} from "@hugeicons-pro/core-stroke-standard";
import { openUrl } from "@/app/helpers";
import AppText from "@/app/components/AppText";
import colors from "@/app/config/colors";
import { HugeiconsIcon } from "@hugeicons/react-native";

export const SocialMediaAccounts = () => {
  const { data } = useSettingsQuery();

  const accounts = useMemo(
    () =>
      data
        ? {
            whatsapp: data.find((s) => s.key === "whatsapp_link")?.value,
            snapchat: data.find((s) => s.key === "snapchat_link")?.value,
            twitter: data.find((s) => s.key === "twitter_link")?.value,
            instagram: data.find((s) => s.key === "instagram_link")?.value,
            youtube: data.find((s) => s.key === "youtube_link")?.value,
          }
        : null,
    [data],
  );

  const noAccounts = useMemo(
    () => !accounts || !Object.values(accounts).some((account) => account),
    [accounts],
  );

  if (noAccounts || !accounts) {
    return null;
  }

  return (
    <View style={spacing.mt6}>
      <AppText
        textAlign="center"
        containerStyle={spacing.mb2}
        weight="medium"
        color={colors.gray["600"]}
      >
        حسابات العائلة
      </AppText>
      <View style={[layout.row, layout.justifyContent.center, layout.gap(18)]}>
        {accounts.whatsapp ? (
          <HugeiconsIcon
            icon={WhatsappIcon}
            width={28}
            height={28}
            onPress={() => openUrl(accounts.whatsapp!)}
          />
        ) : null}
        {accounts.snapchat ? (
          <HugeiconsIcon
            icon={SnapchatIcon}
            width={28}
            height={28}
            onPress={() => openUrl(accounts.snapchat!)}
          />
        ) : null}
        {accounts.twitter ? (
          <HugeiconsIcon
            icon={TwitterIcon}
            width={28}
            height={28}
            onPress={() => openUrl(accounts.twitter!)}
          />
        ) : null}
        {accounts.instagram ? (
          <HugeiconsIcon
            icon={InstagramIcon}
            width={28}
            height={28}
            onPress={() => openUrl(accounts.instagram!)}
          />
        ) : null}

        {accounts.youtube ? (
          <HugeiconsIcon
            icon={YoutubeIcon}
            width={28}
            height={28}
            onPress={() => openUrl(accounts.youtube!)}
          />
        ) : null}
      </View>
    </View>
  );
};
