import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { NavigatorScreenParams } from "@react-navigation/native";
import { createNativeStackNavigator } from "@react-navigation/native-stack";

import colors from "../colors";
import useStartup from "@/app/hooks/useStartup";
import LoginIdentifierScreen from "@/app/modules/Auth/screens/LoginIdentifierScreen";
import LoginOTPScreen from "@/app/modules/Auth/screens/LoginOTPScreen";
import HomeScreen from "@/app/modules/Home/screens/HomeScreen";
import MyAccountScreen from "@/app/modules/MyAccount/screens/MyAccountScreen";
import MyNodeAdditionsScreen from "@/app/modules/MyAccount/screens/MyNodeAdditionsScreen";
import MyNodeChangesScreen from "@/app/modules/MyAccount/screens/MyNodeChangesScreen";
import useUser from "@/app/hooks/useUser";
import { PostScreen } from "@/app/modules/Home/screens/PostScreen";
import PostsScreen from "@/app/modules/Home/screens/PostsScreen";
import { StatsScreen } from "@/app/modules/Home/screens/StatsScreen";
import { TreeChartScreen } from "@/app/modules/Tree/screens/TreeChartScreen";
import { SheetManager } from "react-native-actions-sheet";
import { PaperTreeScreen } from "@/app/modules/Tree/screens/PaperTreeScreen";
import { NodeScreen } from "@/app/modules/Node/screens/NodeScreen";
import LoginViaLinkScreen from "@/app/modules/Auth/screens/LoginViaLinkScreen";
import { Node, Tenant } from "@/app/services/common.services";
import NotificationsScreen from "@/app/modules/Notifications/screens/NotificationsScreen";
import {
  Chart01Icon,
  Home11Icon,
  Tree01Icon,
  User02Icon,
} from "@hugeicons-pro/core-stroke-standard";
import { HugeiconsIcon } from "@hugeicons/react-native";
import { View } from "react-native";
import rounded from "@/app/config/rounded";
import spacing from "@/app/config/spacing";

const Stack = createNativeStackNavigator<Routes>();
const Tab = createBottomTabNavigator<TabsRoutes>();

const tabsOptions = {
  tabBarActiveTintColor: colors.green["600"],
  tabBarInactiveTintColor: colors.green["900"],
};

const HomeStack = () => (
  <Stack.Navigator screenOptions={{ headerShown: false }}>
    <Stack.Screen name="HomeScreen" component={HomeScreen} />
    <Stack.Screen name="StatsScreen" component={StatsScreen} />
    <Stack.Screen name="PostScreen" component={PostScreen} />
    <Stack.Screen name="PostsScreen" component={PostsScreen} />
    <Stack.Screen name="NotificationsScreen" component={NotificationsScreen} />
  </Stack.Navigator>
);

const MyAccountStack = () => (
  <Stack.Navigator screenOptions={{ headerShown: false }}>
    <Stack.Screen name="MyAccountScreen" component={MyAccountScreen} />
    <Stack.Screen
      name="MyNodeAdditionsScreen"
      component={MyNodeAdditionsScreen}
    />
    <Stack.Screen name="MyNodeChangesScreen" component={MyNodeChangesScreen} />
  </Stack.Navigator>
);

const TreeStack = () => (
  <Stack.Navigator screenOptions={{ headerShown: false }}>
    <Stack.Screen name="TreeChartScreen" component={TreeChartScreen} />
    <Stack.Screen name="PaperTreeScreen" component={PaperTreeScreen} />
    <Stack.Screen name="NodeScreen" component={NodeScreen} />
  </Stack.Navigator>
);

const Tabs = () => {
  return (
    <Tab.Navigator
      initialRouteName="Home"
      screenOptions={{ headerShown: false }}
    >
      <Tab.Screen
        name="Home"
        component={HomeStack}
        options={{
          tabBarBadgeStyle: { backgroundColor: colors.red["500"] },
          tabBarIcon: ({ focused }) => (
            <View
              style={
                focused
                  ? [
                      { backgroundColor: `${colors.green["500"]}22` },
                      rounded.full,
                      spacing.p2,
                    ]
                  : null
              }
            >
              <HugeiconsIcon icon={Home11Icon} color={colors.green["500"]} />
            </View>
          ),
          title: "الرئيسية",
          ...tabsOptions,
        }}
      />

      <Tab.Screen
        name="Tree"
        component={TreeStack}
        listeners={{
          tabPress: (e) => {
            e.preventDefault();

            SheetManager.show("select-tree-sheet");
          },
        }}
        options={{
          tabBarBadgeStyle: { backgroundColor: colors.red["500"] },
          tabBarIcon: ({ focused }) => (
            <View
              style={
                focused
                  ? [
                      { backgroundColor: `${colors.green["500"]}22` },
                      rounded.full,
                      spacing.p2,
                    ]
                  : null
              }
            >
              <HugeiconsIcon icon={Tree01Icon} color={colors.green["500"]} />
            </View>
          ),
          title: "الشجرة",
          ...tabsOptions,
        }}
      />
      <Tab.Screen
        name="Stats"
        component={StatsScreen}
        options={{
          tabBarBadgeStyle: { backgroundColor: colors.red["500"] },
          tabBarIcon: ({ focused }) => (
            <View
              style={
                focused
                  ? [
                      { backgroundColor: `${colors.green["500"]}22` },
                      rounded.full,
                      spacing.p2,
                    ]
                  : null
              }
            >
              <HugeiconsIcon icon={Chart01Icon} color={colors.green["500"]} />
            </View>
          ),
          title: "الإحصائيات",
          ...tabsOptions,
        }}
      />

      <Tab.Screen
        name="MyAccount"
        component={MyAccountStack}
        options={{
          tabBarBadgeStyle: { backgroundColor: colors.red["500"] },
          tabBarIcon: ({ focused }) => (
            <View
              style={
                focused
                  ? [
                      { backgroundColor: `${colors.green["500"]}22` },
                      rounded.full,
                      spacing.p2,
                    ]
                  : null
              }
            >
              <HugeiconsIcon icon={User02Icon} color={colors.green["500"]} />
            </View>
          ),
          title: "أنا",
          ...tabsOptions,
        }}
      />
    </Tab.Navigator>
  );
};

export const RootStack = () => {
  const { isAppReady } = useStartup();
  const { user } = useUser();

  if (!isAppReady) {
    return null;
  }

  return (
    <Stack.Navigator
      initialRouteName={user ? "Tabs" : "LoginIdentifierScreen"}
      screenOptions={{ headerShown: false }}
    >
      <Stack.Screen name="Tabs" component={Tabs} />
      <Stack.Screen
        name="LoginIdentifierScreen"
        component={LoginIdentifierScreen}
      />
      <Stack.Screen name="LoginViaLinkScreen" component={LoginViaLinkScreen} />
      <Stack.Screen name="LoginOTPScreen" component={LoginOTPScreen} />
    </Stack.Navigator>
  );
};

export type TabsRoutes = {
  Home: undefined | { screen: string };
  Tree: undefined | { screen: string };
  Stats: undefined | { screen: string };
  MyAccount: undefined | { screen: string };
};

export type Routes = {
  Tabs: NavigatorScreenParams<TabsRoutes>;
  HomeScreen: undefined;
  PostScreen: {
    post: any;
  };
  StatsScreen: undefined;
  LoginViaLinkScreen: {
    tenant: Tenant;
    subdomain: string;
  };
  LoginIdentifierScreen:
    | {
        backTo: ScreensNames;
        backToParams?: any;
      }
    | undefined;
  LoginOTPScreen: {
    identifier: string;
    backTo?: ScreensNames;
    backToParams?: any;
  };
  MyAccountScreen: undefined;
  MyNodeAdditionsScreen: undefined;
  MyNodeChangesScreen: undefined;
  PaperTreeScreen: undefined;
  TreeChartScreen: {
    node?: Node;
  };
  NodeScreen: {
    nodeId: number;
  };
  PostsScreen: undefined;
  NotificationsScreen: undefined;
};

export type ScreensNames = keyof Routes;
