// Base Laravel Model Interface
export interface BaseModel {
  id: number;
  created_at: string;
  updated_at: string;
}

// Base Tenant Model Interface
export interface BaseTenantModel extends BaseModel {
  tenant_id: number;
}

export interface Document extends BaseTenantModel {
  title: string;
  description?: string;
  file_path: string;
  file_size: number;
  mime_type: string;
  original_filename: string;
  type: "pdf" | "image";
  // Appended attributes
  formatted_file_size: string;
  file_extension: string;
  file_url: string;
  created_at: string;
  updated_at: string;
}
