import React, { ReactNode, useRef } from "react";
import { Platform, ScrollView, View } from "react-native";
import ActionSheet, {
  ActionSheetRef,
  SheetManager,
  useScrollHandlers,
} from "react-native-actions-sheet";
import { ActionSheetProps } from "react-native-actions-sheet/dist/src/types";

import bg from "../config/bg";
import layout from "../config/layout";
import rounded from "../config/rounded";
import spacing from "../config/spacing";
import { Bounceable } from "./Bounceable";
import { MultiplicationSignIcon } from "@hugeicons-pro/core-stroke-standard";
import colors from "@/app/config/colors";
import { HugeiconsIcon } from "@hugeicons/react-native";

export const AppSheet = (
  props: ActionSheetProps & {
    topStart?: ReactNode;
  },
) => {
  const actionSheetRef = useRef<ActionSheetRef>(null);
  const scrollHandlers = useScrollHandlers<ScrollView>(
    "scrollview-1",
    actionSheetRef,
  );

  return (
    <ActionSheet
      {...props}
      ref={actionSheetRef}
      headerAlwaysVisible
      openAnimationConfig={{ bounciness: 0 }}
    >
      <View
        style={[
          layout.row,
          layout.justifyContent.spaceBetween,
          spacing.p2,
          layout.alignItems.center,
        ]}
      >
        {props.topStart}
        <Bounceable
          contentContainerStyle={[rounded.full, bg.gray["100"], spacing.p1]}
          onPress={() => {
            if (props.id) {
              SheetManager.hide(props.id);
            } else {
              SheetManager.hideAll();
            }
          }}
        >
          <HugeiconsIcon
            icon={MultiplicationSignIcon}
            color={colors.gray["800"]}
            width={24}
            height={24}
          />
        </Bounceable>
      </View>
      <ScrollView
        {...scrollHandlers}
        contentContainerStyle={[
          spacing.p3,
          Platform.OS === "android" ? spacing.mb4 : undefined,
        ]}
      >
        {props.children}
      </ScrollView>
    </ActionSheet>
  );
};
