import { Bounceable } from "@/app/components/Bounceable";
import * as Linking from "expo-linking";
import spacing from "@/app/config/spacing";
import bg from "@/app/config/bg";
import AppText from "@/app/components/AppText";
import React from "react";
import useUser from "@/app/hooks/useUser";
import rounded from "@/app/config/rounded";
import { View } from "react-native";

export const YouLoggedInAsNotMemberBanner = () => {
  const { user } = useUser();

  if (!user || user.resource_name === "member") {
    return null;
  }

  if (user.resource_name === "guest") {
    return (
      <View style={[spacing.px, spacing.py1, bg.green["500"], rounded.lg]}>
        <AppText color="white" textAlign="center" size="xs" weight="bold">
          أنت الأن داخل كزائر
        </AppText>
      </View>
    );
  }

  // user
  return (
    <Bounceable
      onPress={() => Linking.openURL("https://awraq.app")}
      contentContainerStyle={[
        spacing.px,
        spacing.py1,
        bg.green["500"],
        rounded.lg,
      ]}
    >
      <AppText color="white" textAlign="center" size="xs" weight="bold">
        أنت الأن داخل كمسؤول للعائلة،{" "}
        <AppText color="white" textAlign="center" size="xs" weight="medium">
          لتعديل العائلة في الموقع
        </AppText>{" "}
        <AppText underline color="white" textAlign="center" size="xs">
          awraq.app
        </AppText>
      </AppText>
    </Bounceable>
  );
};
