import { Node } from "@/app/services/common.services";
import { Search01Icon } from "@hugeicons-pro/core-stroke-standard";
import AppText from "@/app/components/AppText";
import colors from "@/app/config/colors";
import layout from "@/app/config/layout";
import rounded from "@/app/config/rounded";
import borders from "@/app/config/borders";
import spacing from "@/app/config/spacing";
import { Bounceable } from "@/app/components/Bounceable";
import { SheetManager } from "react-native-actions-sheet";
import bg from "@/app/config/bg";
import { captureEvent, EventType } from "@/app/services/posthog.services";
import { HugeiconsIcon } from "@hugeicons/react-native";

export type Props = {
  onSelectNode: (node: Node) => void;
  url?: string;
};

export const searchPlaceholder = "بحث بالاسم، رقم الجوال...";

export const NodeSearchInput = ({ onSelectNode, url }: Props) => {
  return (
    <Bounceable
      onPress={async () => {
        const data = await SheetManager.show("node-search", {
          payload: { url },
        });

        if (data?.node) {
          captureEvent(EventType.NODE_VIEW, {
            nodeId: data.node.id,
            nodeName: data.node.full_name,
          });
          onSelectNode(data.node);
        }
      }}
      contentContainerStyle={[
        layout.row,
        layout.alignItems.center,
        layout.gap(8),
        rounded.lg,
        borders.md,
        spacing.px3,
        spacing.py2,
        borders.gray["300"],
        bg.white,
      ]}
    >
      <HugeiconsIcon
        icon={Search01Icon}
        width={18}
        height={18}
        color={colors.gray["500"]}
      />
      <AppText color={colors.gray["500"]}>{searchPlaceholder}</AppText>
    </Bounceable>
  );
};
