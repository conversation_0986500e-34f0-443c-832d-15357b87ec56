import React from "react";
import { AppSheet } from "@/app/components/AppSheet";
import { Sheet<PERSON>ana<PERSON>, SheetProps } from "react-native-actions-sheet";
import { View } from "react-native";
import layout from "@/app/config/layout";
import AppText from "@/app/components/AppText";
import {
  AddTeam02Icon,
  MailEdit02Icon,
  ViewIcon,
} from "@hugeicons-pro/core-stroke-standard";
import colors from "@/app/config/colors";
import { Bounceable } from "@/app/components/Bounceable";
import HR from "@/app/components/HR";
import useAppNavigation from "@/app/hooks/useAppNavigation";
import useUser from "@/app/hooks/useUser";
import { HugeiconsIcon } from "@hugeicons/react-native";

export const NodeOptionsSheet = (props: SheetProps<"node-options-sheet">) => {
  const navigation = useAppNavigation();
  const { user } = useUser();

  const onAddNode = async () => {
    await SheetManager.show("node-form", {
      payload: {
        parent: props.payload!.node!,
        formType: "request-add-node",
      },
    });

    SheetManager.hide(props.sheetId);
  };

  const onEditNode = async () => {
    await SheetManager.show("node-form", {
      payload: {
        node: props.payload!.node,
        parent: props.payload!.node.parent!,
        formType: "request-edit-node",
      },
    });

    SheetManager.hide(props.sheetId);
  };

  const onViewOnTreeChart = () => {
    navigation.navigate("Tree", {
      screen: "TreeChartScreen",
      params: { node: props.payload!.node },
    });
    SheetManager.hide(props.sheetId);
  };

  return (
    <AppSheet id={props.sheetId}>
      <View style={[layout.gap(24)]}>
        <Bounceable
          onPress={onViewOnTreeChart}
          contentContainerStyle={[
            layout.row,
            layout.alignItems.center,
            layout.gap(8),
          ]}
        >
          <HugeiconsIcon
            icon={ViewIcon}
            color={colors.gray["900"]}
            width={28}
            height={28}
          />
          <AppText weight="medium" size="md">
            عرض في الشجرة
          </AppText>
        </Bounceable>
        <HR />
        <Bounceable
          onPress={onAddNode}
          disabled={user?.resource_name !== "member"}
          contentContainerStyle={[
            layout.row,
            layout.alignItems.center,
            layout.gap(8),
            user?.resource_name === "member" ? {} : { opacity: 0.5 },
          ]}
        >
          <HugeiconsIcon
            icon={AddTeam02Icon}
            color={colors.gray["900"]}
            width={28}
            height={28}
          />
          <AppText weight="medium" size="md">
            طلب إضافة فرد
          </AppText>
          {user?.resource_name === "member" ? null : (
            <AppText size="xs">متاح فقط لأفراد العائلة</AppText>
          )}
        </Bounceable>
        <Bounceable
          onPress={onEditNode}
          disabled={user?.resource_name !== "member"}
          contentContainerStyle={[
            layout.row,
            layout.alignItems.center,
            layout.gap(8),
            user?.resource_name === "member" ? {} : { opacity: 0.5 },
          ]}
        >
          <HugeiconsIcon
            icon={MailEdit02Icon}
            color={colors.gray["900"]}
            width={26}
            height={26}
          />
          <AppText weight="medium" size="md">
            طلب تعديل فرد
          </AppText>
          {user?.resource_name === "member" ? null : (
            <AppText size="xs">متاح فقط لأفراد العائلة</AppText>
          )}
        </Bounceable>
      </View>
    </AppSheet>
  );
};
