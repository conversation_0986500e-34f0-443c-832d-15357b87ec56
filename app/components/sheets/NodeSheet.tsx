import React from "react";
import AppText from "@/app/components/AppText";
import { AppSheet } from "@/app/components/AppSheet";
import { View } from "react-native";
import spacing from "@/app/config/spacing";
import { Sheet<PERSON>anager, SheetProps } from "react-native-actions-sheet";
import { useNodeQuery } from "@/app/services/common.services";
import Loader from "@/app/components/Loader";
import { getSize } from "@/app/helpers";
import colors from "@/app/config/colors";
import layout from "@/app/config/layout";
import AppButton from "@/app/components/AppButton";
import { useNavigateToNode } from "@/app/hooks/useAppNavigation";
import { NodeBasicInfo } from "@/app/components/NodeBasicInfo";

export const NodeSheet = (props: SheetProps<"node-sheet">) => {
  const navigateToNode = useNavigateToNode();
  const { data: node, isPending } = useNodeQuery(props.payload?.nodeId);

  if (!props.payload?.nodeId) {
    SheetManager.hide("node-sheet");
    return null;
  }

  if (isPending) {
    return (
      <AppSheet id={props.sheetId}>
        <Loader />
      </AppSheet>
    );
  }

  if (!node) {
    SheetManager.hide("node-sheet");
    return null;
  }

  return (
    <AppSheet
      id={props.sheetId}
      topStart={
        <AppText color={colors.gray["600"]}>الرقم التسلسي: {node.id}</AppText>
      }
    >
      <NodeBasicInfo node={node} />
      <View style={[layout.center]}>
        <AppButton
          containerStyle={[spacing.mt8, getSize(120, 40)]}
          onPress={() => {
            navigateToNode(node.id);
            SheetManager.hide(props.sheetId);
          }}
        >
          عرض
        </AppButton>
      </View>
    </AppSheet>
  );
};
