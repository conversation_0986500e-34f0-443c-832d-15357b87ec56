import React from "react";
import AppText from "@/app/components/AppText";
import { AppSheet } from "@/app/components/AppSheet";
import { useSettingsQuery } from "@/app/services/common.services";
import { View } from "react-native";
import rounded from "@/app/config/rounded";
import AppShadow from "@/app/components/AppShadow";
import borders from "@/app/config/borders";
import { getSize } from "@/app/helpers";
import layout from "@/app/config/layout";
import spacing from "@/app/config/spacing";
import { Tree06Icon, HierarchyIcon } from "@hugeicons-pro/core-stroke-standard";
import colors from "@/app/config/colors";
import { Bounceable } from "@/app/components/Bounceable";
import useAppNavigation from "@/app/hooks/useAppNavigation";
import { SheetManager, SheetProps } from "react-native-actions-sheet";
import bg from "@/app/config/bg";
import { HugeiconsIcon } from "@hugeicons/react-native";

export const SelectTreeSheet = (props: SheetProps<"node-sheet">) => {
  const navigation = useAppNavigation();

  const { data: settings } = useSettingsQuery();

  const paperTreeUrl = settings?.find((s) => s.key === "paper_tree_url")?.value;

  return (
    <AppSheet id={props.sheetId}>
      <View style={[layout.center, layout.row, layout.wrap, layout.gap(24)]}>
        {paperTreeUrl ? (
          <Bounceable
            onPress={async () => {
              await SheetManager.hide("select-tree-sheet");
              navigation.navigate("Tree", { screen: "PaperTreeScreen" });
            }}
          >
            <AppShadow
              containerStyle={[
                rounded.xl,
                borders.sm,
                borders.gray["100"],
                getSize(125, 110),
                layout.center,
                bg.white,
              ]}
            >
              <HugeiconsIcon
                icon={Tree06Icon}
                {...getSize(35, 35)}
                style={[spacing.mb3]}
                color={colors.green["600"]}
              />
              <AppText weight="medium" size="md" color={colors.gray["700"]}>
                الشجرة الورقية
              </AppText>
            </AppShadow>
          </Bounceable>
        ) : null}
        <Bounceable
          onPress={async () => {
            await SheetManager.hide("select-tree-sheet");
            navigation.navigate("Tree", { screen: "TreeChartScreen" });
          }}
        >
          <AppShadow
            containerStyle={[
              rounded.xl,
              borders.sm,
              borders.gray["100"],
              getSize(125, 110),
              layout.center,
              bg.white,
            ]}
          >
            <HugeiconsIcon
              icon={HierarchyIcon}
              {...getSize(35, 35)}
              style={[spacing.mb3]}
              color={colors.yellow["600"]}
            />
            <AppText weight="medium" size="md" color={colors.gray["700"]}>
              رسم شجري
            </AppText>
          </AppShadow>
        </Bounceable>
      </View>
    </AppSheet>
  );
};
