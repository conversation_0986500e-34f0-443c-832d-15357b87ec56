import { Linking } from "react-native";
import { Notifier, NotifierComponents } from "react-native-notifier";

import device from "./config/device";
import { Node } from "@/app/services/common.services";
import dayjs, { hijri } from "./config/dayjs";

type ValidateHijriDateParams = {
  year: string | null;
  month: string | null;
  day: string | null;
  startDate?: {
    year: string | null;
    month: string | null;
    day: string | null;
  };
};

export const isValidPhone = (phone: string | null) => {
  return new RegExp(/^(009665|9665|\+9665|05|5)([2503649187])([0-9]{7})$/).test(
    toEnglishDigits(phone!) ?? "",
  );
};

export const toast = (
  options:
    | {
        body: string;
        type: "success" | "info" | "error";
        onPress?: () => void;
      }
    | string,
) => {
  if (typeof options === "string") {
    options = { body: options, type: "success", onPress: undefined };
  }

  Notifier.showNotification({
    description: options.body,
    Component: NotifierComponents.Alert,
    componentProps: {
      alertType: options.type,
    },
    duration: 5000,
    onPress: () => {
      if (typeof options !== "string") {
        options.onPress?.();
        Notifier.hideNotification();
      }
    },
    hideOnPress: false,
  });
};

export const getSize = (
  width: number,
  height: number = 1,
  deviceWidth: number = device.width,
) => {
  const aspectRatio = parseFloat((width / height).toFixed(2));
  const dynamicWidth = parseFloat((width / 360).toFixed(2)) * deviceWidth;
  const dynamicHeight = parseFloat((dynamicWidth / aspectRatio).toFixed(2));

  return {
    width: dynamicWidth,
    height: dynamicHeight,
    aspectRatio,
  };
};

export const toEnglishDigits = (str: string | undefined) => {
  if (!str) {
    return "";
  }

  let e = "۰".charCodeAt(0);
  str = str.replace(/[۰-۹]/g, (t) => t.charCodeAt(0) - e);

  e = "٠".charCodeAt(0);
  str = str.replace(/[٠-٩]/g, (t) => t.charCodeAt(0) - e);

  return str.trim();
};

export const filterObjectsByProperty = <T>(
  array: T[] | undefined,
  property: keyof T,
) => {
  return array?.filter(
    (obj, index, self) =>
      index === self.findIndex((t) => t[property] === obj[property]),
  );
};

export const mergePages = <T extends { data: { id } }>(pages: T[]) => {
  return filterObjectsByProperty(pages, "id");
};

export const openUrl = (link: string) => Linking.openURL(link).catch(noop);

export const noop = () => null;

export const logToSentry = (...message: any[]) => console.info(...message);

export const removeSpaces = (str: string) => str.replace(/\s/g, "");

export const round = (number: number) => Math.round(number * 100) / 100;

export const truncate = (str: string, length: number) =>
  str.length > length ? `${str.slice(0, length)}...` : str;

export const formatFirstThreeNames = (node: Node) => {
  return (
    node.full_name
      ?.split(/ (بن|بنت) /)
      .slice(0, 5)
      .join(" ") ?? node.name
  );
};

export const dateObject = (dateAsString: string) => {
  if (!dateAsString) {
    return {
      year: null,
      month: null,
      day: null,
    };
  }

  const array = dateAsString.split("/");

  if (array.length !== 3) {
    return {
      year: null,
      month: null,
      day: null,
    };
  }

  return {
    year: array[0],
    month: array[1],
    day: array[2],
  };
};

export const getSubdomain = (url: string | null | undefined) => {
  if (!url) {
    return null;
  }

  try {
    const { hostname } = new URL(url);
    const parts = hostname.split(".");

    // Check if there are more than two parts (subdomain + domain + TLD)
    if (parts.length > 2) {
      // Check if the first part is 'www' and ignore it
      return parts[0] === "www" ? null : parts[0];
    }

    return null;
  } catch (error) {
    console.error("Invalid URL:", error);
    return null;
  }
};

export const isUrl = (url: string) => {
  return new RegExp(
    "^(https?:\\/\\/)?" + // protocol
      "((([a-zA-Z0-9\\-]+\\.)+[a-zA-Z]{2,})|" + // domain name
      "localhost|" + // localhost
      "\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}|" + // OR ip (v4) address
      "\\[?[a-fA-F0-9]*:[a-fA-F0-9:]+\\]?)" + // OR ip (v6) address
      "(\\:\\d+)?(\\/[-a-zA-Z0-9+&@#\\/%?=~_|!:,.;]*)*" + // port and path
      "(\\?[;&a-zA-Z0-9+%_.~#?&=]*)?" + // query string
      "(\\#[-a-zA-Z0-9+&@#/%=~_|]*)?$",
    "i",
  ).test(url);
};

export const formatDate = (date: string) => dayjs(date).format("DD/MM/YYYY");

export const isValidHijriDate = ({
  year,
  month,
  day,
  startDate,
}: ValidateHijriDateParams) => {
  console.log(year, month, day);
  // If all fields are empty, it's considered valid
  if (!year && !month && !day) {
    return true;
  }

  // If any field is empty, it's considered invalid
  if (!year || !month || !day) {
    return false;
  }

  year = toEnglishDigits(year);
  month = toEnglishDigits(month);
  day = toEnglishDigits(day);

  const yearNum = parseInt(year);
  const monthNum = parseInt(month);
  const dayNum = parseInt(day);
  // Basic Hijri calendar validation
  if (monthNum < 1 || monthNum > 12) {
    return false;
  }
  if (dayNum < 1 || dayNum > 30) {
    return false;
  }

  if (yearNum < 1) {
    return false;
  }
  // Get current Hijri year
  const currentHijriDate = hijri.toHijri(dayjs().format("DD/MM/YYYY"), "/");

  // Prevent future dates
  if (yearNum > currentHijriDate.year) {
    return false;
  }
  // Validate against start date if provided
  if (startDate?.year) {
    const startYear = parseInt(startDate.year);
    if (startYear > yearNum) {
      return false;
    }
  }

  return true;
};
