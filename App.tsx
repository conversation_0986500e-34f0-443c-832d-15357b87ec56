import { NavigationContainer } from "@react-navigation/native";
import * as Sentry from "@sentry/react-native";
import { PersistQueryClientProvider } from "@tanstack/react-query-persist-client";
import { StatusBar } from "react-native";
import { Sheet<PERSON>rovider } from "react-native-actions-sheet";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { NotifierWrapper } from "react-native-notifier";
import { SafeAreaProvider } from "react-native-safe-area-context";

import { RootStack } from "./app/config/navigation/navigation";
import queryClient, { clientPersister } from "./app/config/react-query";

import "./app/config/sheets";
import { PostHogProvider } from "posthog-react-native";
import { postHog } from "@/app/services/posthog.services";
import * as SplashScreen from "expo-splash-screen";
import * as Notifications from "expo-notifications";

Sentry.init({
  dsn: "https://<EMAIL>/4507703615160320",
  // Set tracesSampleRate to 1.0 to capture 100% of transactions for performance monitoring.
  // We recommend adjusting this value in production.
  tracesSampleRate: 0.2,
  enabled: !__DEV__,
  _experiments: {
    // profilesSampleRate is relative to tracesSampleRate.
    // Here, we'll capture profiles for 100% of transactions.
    profilesSampleRate: 0.2,
  },
});

SplashScreen.preventAutoHideAsync();

SplashScreen.setOptions({
  fade: true,
});

Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: false,
    shouldSetBadge: false,
  }),
});

function App() {
  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <NotifierWrapper>
        <StatusBar
          translucent
          backgroundColor="transparent"
          barStyle="dark-content"
        />
        <SafeAreaProvider>
          <PersistQueryClientProvider
            client={queryClient}
            persistOptions={{ persister: clientPersister }}
          >
            <NavigationContainer>
              <PostHogProvider autocapture client={postHog}>
                <SheetProvider>
                  <RootStack />
                </SheetProvider>
              </PostHogProvider>
            </NavigationContainer>
          </PersistQueryClientProvider>
        </SafeAreaProvider>
      </NotifierWrapper>
    </GestureHandlerRootView>
  );
}

export default Sentry.wrap(App);
